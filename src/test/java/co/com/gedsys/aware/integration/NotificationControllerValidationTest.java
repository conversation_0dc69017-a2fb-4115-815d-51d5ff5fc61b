package co.com.gedsys.aware.integration;

import co.com.gedsys.aware.adapters.outbound.services.NotificationServices;
import co.com.gedsys.aware.ports.models.Action;
import co.com.gedsys.aware.ports.models.ActionType;
import co.com.gedsys.aware.ports.models.CreateNotificationRequest;
import co.com.gedsys.aware.ports.models.NotificationType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;
import java.util.Collections;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest
class NotificationControllerValidationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ObjectMapper objectMapper;

    @MockBean
    private NotificationServices notificationServices;

    @MockBean
    private SimpMessagingTemplate simpMessagingTemplate;

    @Test
    void testValidRequest() throws Exception {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("Test notification");
        request.setStakeholders(Arrays.asList("user1", "user2"));
        request.setAction(new Action(ActionType.link, "https://example.com", "Ver más"));

        mockMvc.perform(post("/api/v1/notifications")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isCreated());
    }

    @Test
    void testInvalidRequestMissingType() throws Exception {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(null);
        request.setTitle("Test notification");
        request.setStakeholders(Arrays.asList("user1"));

        mockMvc.perform(post("/api/v1/notifications")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").value("Validation Failed"))
                .andExpect(jsonPath("$.errors.type").value("El tipo de notificación es obligatorio"));
    }

    @Test
    void testInvalidRequestBlankTitle() throws Exception {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("");
        request.setStakeholders(Arrays.asList("user1"));

        mockMvc.perform(post("/api/v1/notifications")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").value("Validation Failed"))
                .andExpect(jsonPath("$.errors.title").value("El título es obligatorio"));
    }

    @Test
    void testInvalidRequestEmptyStakeholders() throws Exception {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("Test notification");
        request.setStakeholders(Collections.emptyList());

        mockMvc.perform(post("/api/v1/notifications")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").value("Validation Failed"))
                .andExpect(jsonPath("$.errors.stakeholders").value("Debe especificar al menos un stakeholder"));
    }

    @Test
    void testInvalidRequestTitleTooLong() throws Exception {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("a".repeat(256)); // 256 caracteres
        request.setStakeholders(Arrays.asList("user1"));

        mockMvc.perform(post("/api/v1/notifications")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").value("Validation Failed"))
                .andExpect(jsonPath("$.errors.title").value("El título no puede exceder 255 caracteres"));
    }

    @Test
    void testInvalidActionValidation() throws Exception {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("Test notification");
        request.setStakeholders(Arrays.asList("user1"));
        request.setAction(new Action(null, "", ""));

        mockMvc.perform(post("/api/v1/notifications")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.error").value("Validation Failed"))
                .andExpect(jsonPath("$.errors").exists());
    }
}
