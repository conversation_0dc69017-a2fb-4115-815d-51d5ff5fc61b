package co.com.gedsys.aware.validation;

import co.com.gedsys.aware.ports.models.Action;
import co.com.gedsys.aware.ports.models.ActionType;
import co.com.gedsys.aware.ports.models.CreateNotificationRequest;
import co.com.gedsys.aware.ports.models.NotificationType;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.Validation;
import jakarta.validation.Validator;
import jakarta.validation.ValidatorFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

class CreateNotificationRequestValidationTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    void testValidRequest() {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("Test notification");
        request.setStakeholders(Arrays.asList("user1", "user2"));
        request.setAction(new Action(ActionType.link, "https://example.com", "Ver más"));

        Set<ConstraintViolation<CreateNotificationRequest>> violations = validator.validate(request);
        assertTrue(violations.isEmpty());
    }

    @Test
    void testNullType() {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(null);
        request.setTitle("Test notification");
        request.setStakeholders(Arrays.asList("user1"));

        Set<ConstraintViolation<CreateNotificationRequest>> violations = validator.validate(request);
        assertEquals(1, violations.size());
        assertEquals("El tipo de notificación es obligatorio", violations.iterator().next().getMessage());
    }

    @Test
    void testBlankTitle() {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("");
        request.setStakeholders(Arrays.asList("user1"));

        Set<ConstraintViolation<CreateNotificationRequest>> violations = validator.validate(request);
        assertEquals(1, violations.size());
        assertEquals("El título es obligatorio", violations.iterator().next().getMessage());
    }

    @Test
    void testNullTitle() {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle(null);
        request.setStakeholders(Arrays.asList("user1"));

        Set<ConstraintViolation<CreateNotificationRequest>> violations = validator.validate(request);
        assertEquals(1, violations.size());
        assertEquals("El título es obligatorio", violations.iterator().next().getMessage());
    }

    @Test
    void testTitleTooLong() {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("a".repeat(256)); // 256 caracteres
        request.setStakeholders(Arrays.asList("user1"));

        Set<ConstraintViolation<CreateNotificationRequest>> violations = validator.validate(request);
        assertEquals(1, violations.size());
        assertEquals("El título no puede exceder 255 caracteres", violations.iterator().next().getMessage());
    }

    @Test
    void testEmptyStakeholders() {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("Test notification");
        request.setStakeholders(Collections.emptyList());

        Set<ConstraintViolation<CreateNotificationRequest>> violations = validator.validate(request);
        assertEquals(1, violations.size());
        assertEquals("Debe especificar al menos un stakeholder", violations.iterator().next().getMessage());
    }

    @Test
    void testNullStakeholders() {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("Test notification");
        request.setStakeholders(null);

        Set<ConstraintViolation<CreateNotificationRequest>> violations = validator.validate(request);
        assertEquals(1, violations.size());
        assertEquals("Debe especificar al menos un stakeholder", violations.iterator().next().getMessage());
    }

    @Test
    void testBlankStakeholder() {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("Test notification");
        request.setStakeholders(Arrays.asList("user1", "", "user3"));

        Set<ConstraintViolation<CreateNotificationRequest>> violations = validator.validate(request);
        assertEquals(1, violations.size());
        assertEquals("Los stakeholders no pueden estar vacíos", violations.iterator().next().getMessage());
    }

    @Test
    void testTooManyStakeholders() {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("Test notification");
        
        // Crear una lista con 101 stakeholders
        List<String> stakeholders = Collections.nCopies(101, "user");
        request.setStakeholders(stakeholders);

        Set<ConstraintViolation<CreateNotificationRequest>> violations = validator.validate(request);
        assertEquals(1, violations.size());
        assertEquals("No se pueden especificar más de 100 stakeholders", violations.iterator().next().getMessage());
    }

    @Test
    void testInvalidAction() {
        CreateNotificationRequest request = new CreateNotificationRequest();
        request.setType(NotificationType.info);
        request.setTitle("Test notification");
        request.setStakeholders(Arrays.asList("user1"));
        request.setAction(new Action(null, "", ""));

        Set<ConstraintViolation<CreateNotificationRequest>> violations = validator.validate(request);
        assertEquals(3, violations.size()); // type null, url blank, text blank
    }
}
