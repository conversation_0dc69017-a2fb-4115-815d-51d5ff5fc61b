package co.com.gedsys.aware.adapters.inbound;

import co.com.gedsys.aware.adapters.outbound.services.NotificationServices;
import co.com.gedsys.aware.ports.models.Notification;
import co.com.gedsys.aware.ports.models.NotificationStatus;
import co.com.gedsys.aware.ports.models.NotificationType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NotificationControllerTest {

    @Mock
    private NotificationServices notificationServices;

    @InjectMocks
    private NotificationController notificationController;

    private Notification testNotification;
    private Page<Notification> testPage;

    @BeforeEach
    void setUp() {
        testNotification = new Notification();
        testNotification.setId(UUID.randomUUID());
        testNotification.setType(NotificationType.info);
        testNotification.setTitle("Test Notification");
        testNotification.setOwner("testuser");
        testNotification.setTimestamp(LocalDateTime.now());
        testNotification.setStatus(NotificationStatus.unread);

        testPage = new PageImpl<>(List.of(testNotification));
    }

    @Test
    void getNotifications_WithValidHeader_ShouldReturnNotifications() {
        when(notificationServices.findNotificationsWithFilters(
                eq("testuser"),
                eq(NotificationStatus.unread),
                eq("2024-01-01"),
                eq("2024-01-31"),
                any(Pageable.class)
        )).thenReturn(testPage);

        ResponseEntity<Page<Notification>> response = notificationController.getNotifications(
                "testuser", NotificationStatus.unread, "2024-01-01", "2024-01-31", 0, 10);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1, response.getBody().getContent().size());
        assertEquals("Test Notification", response.getBody().getContent().get(0).getTitle());
    }

    @Test
    void getNotifications_WithNullUsername_ShouldReturnBadRequest() {
        ResponseEntity<Page<Notification>> response = notificationController.getNotifications(
                null, NotificationStatus.unread, "2024-01-01", "2024-01-31", 0, 10);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    void getNotifications_WithEmptyUsername_ShouldReturnBadRequest() {
        ResponseEntity<Page<Notification>> response = notificationController.getNotifications(
                "", NotificationStatus.unread, "2024-01-01", "2024-01-31", 0, 10);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    void getNotifications_WithWhitespaceUsername_ShouldReturnBadRequest() {
        ResponseEntity<Page<Notification>> response = notificationController.getNotifications(
                "   ", NotificationStatus.unread, "2024-01-01", "2024-01-31", 0, 10);

        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNull(response.getBody());
    }

    @Test
    void getNotifications_WithUsernameWithSpaces_ShouldTrimAndProcess() {
        when(notificationServices.findNotificationsWithFilters(
                eq("testuser"),
                any(),
                any(),
                any(),
                any(Pageable.class)
        )).thenReturn(testPage);

        ResponseEntity<Page<Notification>> response = notificationController.getNotifications(
                "  testuser  ", null, null, null, 0, 10);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    void getNotifications_WithOptionalParameters_ShouldWork() {
        when(notificationServices.findNotificationsWithFilters(
                eq("testuser"),
                eq(null),
                eq(null),
                eq(null),
                any(Pageable.class)
        )).thenReturn(testPage);

        ResponseEntity<Page<Notification>> response = notificationController.getNotifications(
                "testuser", null, null, null, 0, 10);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
    }

    @Test
    void getNotifications_WithDefaultPagination_ShouldUseDefaults() {
        when(notificationServices.findNotificationsWithFilters(
                eq("testuser"), 
                any(), 
                any(), 
                any(), 
                eq(PageRequest.of(0, 10))
        )).thenReturn(testPage);

        ResponseEntity<Page<Notification>> response = notificationController.getNotifications(
                "testuser", null, null, null, 0, 10);

        assertEquals(HttpStatus.OK, response.getStatusCode());
    }

    @Test
    void getNotifications_WithReadStatus_ShouldWork() {
        when(notificationServices.findNotificationsWithFilters(
                eq("testuser"),
                eq(NotificationStatus.read),
                any(),
                any(),
                any(Pageable.class)
        )).thenReturn(testPage);

        ResponseEntity<Page<Notification>> response = notificationController.getNotifications(
                "testuser", NotificationStatus.read, null, null, 0, 10);

        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
    }
}
