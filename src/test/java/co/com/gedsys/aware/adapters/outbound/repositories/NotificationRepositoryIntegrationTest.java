package co.com.gedsys.aware.adapters.outbound.repositories;

import co.com.gedsys.aware.infrastructure.persistence.entities.NotificationEntity;
import co.com.gedsys.aware.infrastructure.persistence.repositories.NotificationJpaRepository;
import co.com.gedsys.aware.ports.models.NotificationStatus;
import co.com.gedsys.aware.ports.models.NotificationType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@DataJpaTest
@ActiveProfiles("test")
class NotificationRepositoryIntegrationTest {

    @Autowired
    private NotificationJpaRepository notificationJpaRepository;

    private NotificationEntity testEntity1;
    private NotificationEntity testEntity2;
    private Pageable pageable;

    @BeforeEach
    void setUp() {
        notificationJpaRepository.deleteAll();
        
        testEntity1 = NotificationEntity.builder()
                .type(NotificationType.info)
                .title("Test Notification 1")
                .owner("testuser")
                .timestamp(LocalDateTime.now().minusHours(1))
                .status(NotificationStatus.unread)
                .build();

        testEntity2 = NotificationEntity.builder()
                .type(NotificationType.warning)
                .title("Test Notification 2")
                .owner("anotheruser")
                .timestamp(LocalDateTime.now())
                .status(NotificationStatus.read)
                .build();

        notificationJpaRepository.save(testEntity1);
        notificationJpaRepository.save(testEntity2);

        pageable = PageRequest.of(0, 10);
    }

    @Test
    void findByOwnerOrderByTimestampDesc_ShouldReturnCorrectResults() {
        Page<NotificationEntity> result = notificationJpaRepository.findByOwnerOrderByTimestampDesc("testuser", pageable);
        
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals("Test Notification 1", result.getContent().get(0).getTitle());
        assertEquals("testuser", result.getContent().get(0).getOwner());
    }

    @Test
    void findByOwnerOrderByTimestampDesc_WithNonExistentUser_ShouldReturnEmpty() {
        Page<NotificationEntity> result = notificationJpaRepository.findByOwnerOrderByTimestampDesc("nonexistent", pageable);
        
        assertNotNull(result);
        assertEquals(0, result.getTotalElements());
        assertTrue(result.getContent().isEmpty());
    }

    @Test
    void findAll_ShouldReturnAllNotifications() {
        List<NotificationEntity> all = notificationJpaRepository.findAll();
        
        assertEquals(2, all.size());
        
        all.forEach(entity -> {
            System.out.println("ID: " + entity.getId() + ", Owner: '" + entity.getOwner() + "', Title: " + entity.getTitle());
        });
    }

    @Test
    void findByOwnerOrderByTimestampDesc_WithExactMatch_ShouldWork() {
        String exactOwner = testEntity1.getOwner();
        System.out.println("Buscando por owner exacto: '" + exactOwner + "'");
        
        Page<NotificationEntity> result = notificationJpaRepository.findByOwnerOrderByTimestampDesc(exactOwner, pageable);
        
        assertNotNull(result);
        assertEquals(1, result.getTotalElements());
        assertEquals(exactOwner, result.getContent().get(0).getOwner());
    }
}
