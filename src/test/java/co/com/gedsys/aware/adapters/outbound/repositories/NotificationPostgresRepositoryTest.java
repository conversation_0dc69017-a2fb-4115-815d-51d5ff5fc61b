package co.com.gedsys.aware.adapters.outbound.repositories;

import co.com.gedsys.aware.infrastructure.persistence.entities.NotificationEntity;
import co.com.gedsys.aware.infrastructure.persistence.repositories.NotificationJpaRepository;
import co.com.gedsys.aware.ports.models.Notification;
import co.com.gedsys.aware.ports.models.NotificationStatus;
import co.com.gedsys.aware.ports.models.NotificationType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class NotificationPostgresRepositoryTest {

    @Mock
    private NotificationJpaRepository notificationJpaRepository;

    @InjectMocks
    private NotificationPostgresRepository notificationPostgresRepository;

    private NotificationEntity testEntity;
    private Pageable pageable;

    @BeforeEach
    void setUp() {
        testEntity = NotificationEntity.builder()
                .id(UUID.randomUUID())
                .type(NotificationType.info)
                .title("Test Notification")
                .owner("testuser")
                .timestamp(LocalDateTime.now())
                .status(NotificationStatus.unread)
                .build();

        pageable = PageRequest.of(0, 10);
    }

    @Test
    void findNotificationsWithFilters_WithDateRange_ShouldConvertDatesCorrectly() {
        String startDate = "2024-01-01";
        String endDate = "2024-01-31";

        Page<NotificationEntity> mockPage = new PageImpl<>(List.of(testEntity));
        when(notificationJpaRepository.findByOwnerAndStatusAndTimestampBetweenOrderByTimestampDesc(
                eq("testuser"),
                eq(NotificationStatus.unread),
                any(LocalDateTime.class),
                any(LocalDateTime.class),
                eq(pageable)
        )).thenReturn(mockPage);

        Page<Notification> result = notificationPostgresRepository.findNotificationsWithFilters(
                "testuser", NotificationStatus.unread, startDate, endDate, pageable);

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals("Test Notification", result.getContent().get(0).getTitle());
        assertEquals("testuser", result.getContent().get(0).getOwner());
    }

    @Test
    void findNotificationsWithFilters_WithNullDates_ShouldHandleNullValues() {
        Page<NotificationEntity> mockPage = new PageImpl<>(List.of(testEntity));
        when(notificationJpaRepository.findByOwnerAndStatusOrderByTimestampDesc(
                eq("testuser"),
                eq(NotificationStatus.unread),
                eq(pageable)
        )).thenReturn(mockPage);

        Page<Notification> result = notificationPostgresRepository.findNotificationsWithFilters(
                "testuser", NotificationStatus.unread, null, null, pageable);

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
    }

    @Test
    void findNotificationsWithFilters_WithEmptyDates_ShouldHandleEmptyStrings() {
        Page<NotificationEntity> mockPage = new PageImpl<>(List.of(testEntity));
        when(notificationJpaRepository.findByOwnerAndStatusOrderByTimestampDesc(
                eq("testuser"),
                eq(NotificationStatus.unread),
                eq(pageable)
        )).thenReturn(mockPage);

        Page<Notification> result = notificationPostgresRepository.findNotificationsWithFilters(
                "testuser", NotificationStatus.unread, "", "", pageable);

        assertNotNull(result);
        assertEquals(1, result.getContent().size());
    }
}
