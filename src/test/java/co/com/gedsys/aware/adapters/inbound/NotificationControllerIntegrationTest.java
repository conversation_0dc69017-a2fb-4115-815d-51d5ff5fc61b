package co.com.gedsys.aware.adapters.inbound;

import co.com.gedsys.aware.adapters.outbound.services.NotificationServices;
import co.com.gedsys.aware.ports.models.Notification;
import co.com.gedsys.aware.ports.models.NotificationStatus;
import co.com.gedsys.aware.ports.models.NotificationType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(NotificationController.class)
class NotificationControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private NotificationServices notificationServices;

    @Autowired
    private ObjectMapper objectMapper;

    private Notification testNotification;
    private Page<Notification> testPage;

    @BeforeEach
    void setUp() {
        testNotification = new Notification();
        testNotification.setId(UUID.randomUUID());
        testNotification.setType(NotificationType.info);
        testNotification.setTitle("Test Notification");
        testNotification.setOwner("testuser");
        testNotification.setTimestamp(LocalDateTime.now());
        testNotification.setStatus(NotificationStatus.unread);

        testPage = new PageImpl<>(List.of(testNotification));
    }

    @Test
    void getNotifications_WithValidHeader_ShouldReturn200() throws Exception {
        when(notificationServices.findNotificationsWithFilters(
                eq("testuser"), 
                any(), 
                any(), 
                any(), 
                any(Pageable.class)
        )).thenReturn(testPage);

        mockMvc.perform(get("/api/v1/notifications")
                .header("X-UserName", "testuser")
                .param("status", "unread")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].title").value("Test Notification"))
                .andExpect(jsonPath("$.content[0].owner").value("testuser"));
    }

    @Test
    void getNotifications_WithoutHeader_ShouldReturn400() throws Exception {
        mockMvc.perform(get("/api/v1/notifications")
                .param("status", "unread")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void getNotifications_WithEmptyHeader_ShouldReturn400() throws Exception {
        mockMvc.perform(get("/api/v1/notifications")
                .header("X-UserName", "")
                .param("status", "unread")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void getNotifications_WithWhitespaceHeader_ShouldReturn400() throws Exception {
        mockMvc.perform(get("/api/v1/notifications")
                .header("X-UserName", "   ")
                .param("status", "unread")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }

    @Test
    void getNotifications_WithDateFilters_ShouldReturn200() throws Exception {
        when(notificationServices.findNotificationsWithFilters(
                eq("testuser"),
                eq(NotificationStatus.unread),
                eq("2024-01-01"),
                eq("2024-01-31"),
                any(Pageable.class)
        )).thenReturn(testPage);

        mockMvc.perform(get("/api/v1/notifications")
                .header("X-UserName", "testuser")
                .param("status", "unread")
                .param("startDate", "2024-01-01")
                .param("endDate", "2024-01-31")
                .param("page", "0")
                .param("size", "10")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[0].title").value("Test Notification"));
    }

    @Test
    void getNotifications_WithDefaultPagination_ShouldReturn200() throws Exception {
        when(notificationServices.findNotificationsWithFilters(
                eq("testuser"), 
                any(), 
                any(), 
                any(), 
                any(Pageable.class)
        )).thenReturn(testPage);

        mockMvc.perform(get("/api/v1/notifications")
                .header("X-UserName", "testuser")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray());
    }

    @Test
    void getNotifications_WithUsernameContainingSpaces_ShouldTrimAndReturn200() throws Exception {
        when(notificationServices.findNotificationsWithFilters(
                eq("testuser"), 
                any(), 
                any(), 
                any(), 
                any(Pageable.class)
        )).thenReturn(testPage);

        mockMvc.perform(get("/api/v1/notifications")
                .header("X-UserName", "  testuser  ")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[0].owner").value("testuser"));
    }

    @Test
    void getNotifications_WithEnumStatus_ShouldReturn200() throws Exception {
        when(notificationServices.findNotificationsWithFilters(
                eq("testuser"),
                eq(NotificationStatus.read),
                any(),
                any(),
                any(Pageable.class)
        )).thenReturn(testPage);

        mockMvc.perform(get("/api/v1/notifications")
                .header("X-UserName", "testuser")
                .param("status", "read")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content[0].title").value("Test Notification"));
    }

    @Test
    void getNotifications_WithInvalidEnumStatus_ShouldReturn400() throws Exception {
        mockMvc.perform(get("/api/v1/notifications")
                .header("X-UserName", "testuser")
                .param("status", "invalid_status")
                .contentType(MediaType.APPLICATION_JSON))
                .andExpect(status().isBadRequest());
    }
}
