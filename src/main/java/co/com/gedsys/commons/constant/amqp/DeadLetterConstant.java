package co.com.gedsys.commons.constant.amqp;

/***
 * Dead Letter Exchange (DLX) and Dead Letter Queue (DLQ) are RabbitMQ concepts that allow for the
 * routing of messages that cannot be processed to a separate queue, known as the dead letter queue.
 * DLK is the routing key used for messages that are routed to the dead letter queue.
 */
public final class DeadLetterConstant {

    private DeadLetterConstant() {
    }

    public static final String DLX = "gedsys.dlx";

    public static final String DLK = "gedsys.dlk";

    public static final String DLQ = "gedsys.dlq";
}
