package co.com.gedsys.commons.interfaces;

import com.rabbitmq.client.Channel;
import org.springframework.amqp.AmqpRejectAndDontRequeueException;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.AmqpHeaders;
import org.springframework.messaging.handler.annotation.Header;

import java.io.IOException;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public abstract class AbstractRabbitMQListener<T> {
    private static final Logger log = LoggerFactory.getLogger(AbstractRabbitMQListener.class);
    private static final int MAX_RETRIES = 3;
    private static final String RETRY_COUNT_HEADER = "X-Retry-Count";

    private final RabbitTemplate rabbitTemplate;

    protected AbstractRabbitMQListener(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }

    public void processMessage(T payload,
                               Message message,
                               Channel channel,
                               @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        String queueName = message.getMessageProperties().getConsumerQueue();
        log.debug("Processing message from queue '{}' with delivery tag {}", queueName, deliveryTag);

        boolean messageAcknowledged = false;
        try {
            handleMessageProcessing(payload);
            acknowledgeMessage(channel, deliveryTag);
            messageAcknowledged = true;
            log.debug("Successfully processed message from queue '{}' with delivery tag {}", queueName, deliveryTag);
        } catch (Exception error) {
            if (!messageAcknowledged) {
                handleMessageError(message, channel, deliveryTag, error);
            }
        }
    }

    // Método abstracto que las clases hijas deben implementar
    protected abstract void handleMessageProcessing(T messageReceived) throws IOException;

    private void handleMessageError(Message message,
                                    Channel channel,
                                    long deliveryTag,
                                    Exception error) {
        String queueName = message.getMessageProperties().getConsumerQueue();
        log.error("Error processing message from queue '{}' with delivery tag {}: {}",
                 queueName, deliveryTag, error.getMessage(), error);
        int retryCount = getRetryCount(message);
        try {
            if (shouldSendToDlq(retryCount)) {
                sendToDlq(channel, deliveryTag, retryCount);
            } else {
                retryMessage(message, channel, deliveryTag);
            }
        } catch (IOException ioException) {
            handleIoException(ioException, error);
        }
    }

    private boolean shouldSendToDlq(int retryCount) {
        return retryCount >= MAX_RETRIES;
    }

    private void sendToDlq(Channel channel, long deliveryTag, int retryCount) throws IOException {
        try {
            channel.basicReject(deliveryTag, false);
            log.warn("Message with delivery tag {} sent to DLQ after {} retries", deliveryTag, retryCount);
        } catch (IOException e) {
            if (e.getMessage() != null && e.getMessage().contains("unknown delivery tag")) {
                log.debug("Attempted to reject message with unknown delivery tag {}, message may have been already processed", deliveryTag);
            } else {
                log.error("Failed to reject message with delivery tag {}: {}", deliveryTag, e.getMessage(), e);
                throw e;
            }
        }
    }

    private void retryMessage(Message message, Channel channel, long deliveryTag) throws IOException {
        int currentRetryCount = getRetryCount(message);
        String queueName = message.getMessageProperties().getConsumerQueue();
        log.info("Retrying message from queue '{}' with delivery tag {} (attempt {} of {})",
                queueName, deliveryTag, currentRetryCount + 1, MAX_RETRIES);
        incrementRetryCount(message);
        acknowledgeMessage(channel, deliveryTag);
    }

    protected void acknowledgeMessage(Channel channel, long deliveryTag) throws IOException {
        try {
            channel.basicAck(deliveryTag, false);
            log.debug("Successfully acknowledged message with delivery tag {}", deliveryTag);
        } catch (IOException e) {
            if (e.getMessage() != null && e.getMessage().contains("unknown delivery tag")) {
                log.debug("Attempted to acknowledge message with unknown delivery tag {}, message may have been already processed", deliveryTag);
            } else {
                log.error("Failed to acknowledge message with delivery tag {}: {}", deliveryTag, e.getMessage(), e);
                throw e;
            }
        }
    }

    private void handleIoException(IOException ioException, Exception originalError) {
        log.error("IO Error while handling message - Original error: {}, IO error: {}",
                 originalError.getMessage(), ioException.getMessage(), ioException);
        throw new AmqpRejectAndDontRequeueException("Failed to process message", originalError);
    }

    private int getRetryCount(Message message) {
        MessageProperties properties = message.getMessageProperties();
        Integer retryCount = (Integer) properties.getHeaders().get(RETRY_COUNT_HEADER);
        return retryCount != null ? retryCount : 0;
    }

    private void incrementRetryCount(Message message) {
        int retryCount = getRetryCount(message);
        int newRetryCount = retryCount + 1;
        MessageProperties newProperties = new MessageProperties();
        newProperties.getHeaders().putAll(message.getMessageProperties().getHeaders());
        newProperties.getHeaders().put(RETRY_COUNT_HEADER, newRetryCount);

        Message newMessage = new Message(message.getBody(), newProperties);
        MessageProperties oldProperties = message.getMessageProperties();
        String queueName = oldProperties.getConsumerQueue();

        log.debug("Republishing message to queue '{}' with retry count {}", queueName, newRetryCount);
        rabbitTemplate.send(queueName, newMessage);
    }
}
