package co.com.gedsys.commons.config.bpm_messaging;

import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

import co.com.gedsys.commons.config.DeadLetterDefaultConfig;
import co.com.gedsys.commons.constant.amqp.QueueName;

import static co.com.gedsys.commons.constant.amqp.DeadLetterConstant.DLK;
import static co.com.gedsys.commons.constant.amqp.DeadLetterConstant.DLX;

@AutoConfiguration
@AutoConfigureAfter({ DeadLetterDefaultConfig.class })
@ConditionalOnClass({ Queue.class })
public class BpmQueueConfig {
  
    @Bean(name = "tareasActualizadasQueue")
    @ConditionalOnMissingBean(name = "tareasActualizadasQueue")
    Queue tareasActualizadasQueue() {
        return QueueBuilder.durable(QueueName.TAREAS_ACTUALIZADAS)
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(DLK)
                .build();
    }

    @Bean(name = "proceduresQueue")
    @ConditionalOnMissingBean(name = "proceduresQueue")
    Queue proceduresQueue() {
        return QueueBuilder.durable(QueueName.PROCEDURES)
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(DLK)
                .build();
    }

    @Bean(name = "documentaryLoadCompletedProceduresQueue")
    @ConditionalOnMissingBean(name = "documentaryLoadCompletedProceduresQueue")
    Queue documentaryLoadCompletedProceduresQueue() {
        return QueueBuilder.durable(QueueName.DOCUMENTARY_LOAD_COMPLETED)
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(DLK)
                .build();
    }
}
