package co.com.gedsys.commons.config.notifications_messaging;

import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

import co.com.gedsys.commons.config.DeadLetterDefaultConfig;
import co.com.gedsys.commons.constant.amqp.QueueName;

import static co.com.gedsys.commons.constant.amqp.DeadLetterConstant.DLK;
import static co.com.gedsys.commons.constant.amqp.DeadLetterConstant.DLX;

@AutoConfiguration
@AutoConfigureAfter({ DeadLetterDefaultConfig.class })
@ConditionalOnClass({ Queue.class })
public class NotificacionQueueConfig {
    
    @Bean(name = "notificacionesDocumentosProcesadosQueue")
    @ConditionalOnMissingBean(name = "notificacionesDocumentosProcesadosQueue")
    Queue notificacionesDocumentosProcesadosQueue() {
        return QueueBuilder.durable(QueueName.NOTIFICACIONES_DOCUMENTOS_PROCESADOS)
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(DLK)
                .build();
    }
}
