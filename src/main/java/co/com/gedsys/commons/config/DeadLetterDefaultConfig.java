package co.com.gedsys.commons.config;

import org.springframework.amqp.core.*;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.DependsOn;

import static co.com.gedsys.commons.constant.amqp.DeadLetterConstant.*;

/**
 * Configuración automática por defecto para el manejo de Dead Letter Queue en AMQP.
 * Esta clase se encarga de establecer la configuración básica necesaria para el manejo
 * de mensajes muertos en un sistema de mensajería AMQP.
 */
@AutoConfiguration
@ConditionalOnClass({TopicExchange.class, Queue.class, Binding.class})
public class DeadLetterDefaultConfig {

    /**
     * Crea y configura el exchange para Dead Letter.
     * Este exchange será el punto donde se redirigirán los mensajes que no pueden ser procesados.
     *
     * @return TopicExchange configurado para Dead Letter
     */
    @Bean(name = "deadLetterExchange")
    @ConditionalOnMissingBean(name = "deadLetterExchange")
    public TopicExchange deadLetterExchange() {
        return new TopicExchange(DLX, true, false);
    }

    /**
     * Crea y configura la cola de Dead Letter (DLQ).
     * Esta cola almacenará los mensajes que han fallado en su procesamiento normal.
     *
     * @return Queue configurada como Dead Letter Queue
     */
    @Bean(name = "dlq")
    @ConditionalOnMissingBean(name = "dlq")
    public Queue dlq() {
        return QueueBuilder.durable(DLQ)
                .deadLetterExchange(DLX)
                .build();
    }

    /**
     * Establece el binding entre el exchange y la cola de Dead Letter.
     * Este binding define cómo los mensajes serán enrutados hacia la Dead Letter Queue.
     *
     * @param dlq Cola de Dead Letter previamente configurada
     * @param deadLetterExchange Exchange de Dead Letter previamente configurado
     * @return Binding que conecta el exchange con la cola usando la routing key especificada
     */
    @Bean(name = "deadLetterBinding")
    @DependsOn({"deadLetterExchange", "dlq"})
    @ConditionalOnMissingBean(name = "deadLetterBinding")
    public Binding deadLetterBinding(Queue dlq, TopicExchange deadLetterExchange) {
        return BindingBuilder
                .bind(dlq)
                .to(deadLetterExchange)
                .with(DLK);
    }
}
