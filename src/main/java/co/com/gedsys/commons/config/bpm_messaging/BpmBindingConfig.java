package co.com.gedsys.commons.config.bpm_messaging;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

import co.com.gedsys.commons.constant.amqp.RoutingKeyName;

@AutoConfiguration
@AutoConfigureAfter({ BpmExchangeConfig.class, BpmQueueConfig.class })
@ConditionalOnClass({ Binding.class, Queue.class, TopicExchange.class })
public class BpmBindingConfig {
    /**
     * Configura el binding para la cola de tareas actualizadas
     * con el exchange de procesos usando la routing key TAREA_ACTUALIZADA
     * 
     * @param tareasActualizadasQueue Cola de tareas actualizadas
     * @param procesosTopicExchange   Exchange de procesos
     * @return Binding configurado
     */
    @Bean(name = "tareasActualizadasBinding")
    @ConditionalOnMissingBean(name = "tareasActualizadasBinding")
    Binding tareasActualizadasBinding(Queue tareasActualizadasQueue,
            TopicExchange procesosTopicExchange) {
        return BindingBuilder.bind(tareasActualizadasQueue)
                .to(procesosTopicExchange)
                .with(RoutingKeyName.TAREA_ACTUALIZADA);
    }

    @Bean(name = "proceduresUpdatedBinding")
    @ConditionalOnMissingBean(name = "proceduresUpdatedBinding")
    Binding proceduresUpdatedBinding(Queue proceduresQueue,
            TopicExchange procesosTopicExchange) {
        return BindingBuilder.bind(proceduresQueue)
                .to(procesosTopicExchange)
                .with(RoutingKeyName.PROCEDURE_UPDATED);
    }

    @Bean(name = "proceduresResumedBinding")
    @ConditionalOnMissingBean(name = "proceduresResumedBinding")
    Binding proceduresResumedBinding(Queue proceduresQueue,
            TopicExchange procesosTopicExchange) {
        return BindingBuilder.bind(proceduresQueue)
                .to(procesosTopicExchange)
                .with(RoutingKeyName.PROCEDURE_RESUMED);
    }
}
