package co.com.gedsys.commons.config.resources_messaging;

import co.com.gedsys.commons.config.DeadLetterDefaultConfig;
import co.com.gedsys.commons.constant.amqp.QueueName;

import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.QueueBuilder;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

import static co.com.gedsys.commons.constant.amqp.DeadLetterConstant.DLK;
import static co.com.gedsys.commons.constant.amqp.DeadLetterConstant.DLX;

/**
 * Configuración automática para la definición de colas en RabbitMQ.
 * Esta clase se configura después de ExchangeDefaultConfig y DeadLetterDefaultConfig.
 */
@AutoConfiguration
@AutoConfigureAfter({DeadLetterDefaultConfig.class})
@ConditionalOnClass({Queue.class})
public class ResourcesQueueConfig {

    /**
     * Define la cola para recursos.
     * Esta cola se utiliza para la trazabilidad de recursos en el sistema.
     * Los mensajes fallidos se envían a una cola de letra muerta.
     */
    @Bean(name = "resourcesQueue")
    @ConditionalOnMissingBean(name = "resourcesQueue")
    Queue resourcesQueue() {
        return QueueBuilder.durable(QueueName.CORE_RESOURCES)
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(DLK)
                .build();
    }

}
