package co.com.gedsys.commons.config.notifications_messaging;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

import co.com.gedsys.commons.config.bpm_messaging.BpmExchangeConfig;
import co.com.gedsys.commons.constant.amqp.RoutingKeyName;

@AutoConfiguration
@AutoConfigureAfter({ NotificacionQueueConfig.class, BpmExchangeConfig.class })
@ConditionalOnClass({ Binding.class, Queue.class, TopicExchange.class })
public class NotificacionBindingConfig {

    @Bean(name = "notificacionesDocumentosProcesadosBinding")
    @ConditionalOnMissingBean(name = "notificacionesDocumentosProcesadosBinding")
    Binding notificacionesDocumentosProcesadosBinding(
            Queue notificacionesDocumentosProcesadosQueue,
            TopicExchange procesosTopicExchange) {
        return BindingBuilder
                .bind(notificacionesDocumentosProcesadosQueue)
                .to(procesosTopicExchange)
                .with(RoutingKeyName.DOCUMENTO_PROCESADO);
    }

}
