package co.com.gedsys.commons.config.bpm_messaging;

import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

import co.com.gedsys.commons.constant.amqp.ExchangeName;

@AutoConfiguration
@ConditionalOnClass({ TopicExchange.class, ConnectionFactory.class })
public class BpmExchangeConfig {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(BpmExchangeConfig.class);

    /**
     * Crea el exchange para procesos
     *
     * @return TopicExchange configurado para procesos
     */
    @Bean
    @ConditionalOnMissingBean(name = "procesosTopicExchange")
    TopicExchange procesosTopicExchange() {
        log.info("Creando exchange para procesos: {}", ExchangeName.TOPIC_PROCESOS);
        return new TopicExchange(ExchangeName.TOPIC_PROCESOS, true, false);
    }
}
