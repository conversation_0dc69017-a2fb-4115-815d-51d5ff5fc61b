package co.com.gedsys.commons.config;

import org.springframework.amqp.rabbit.config.SimpleRabbitListenerContainerFactory;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.util.ErrorHandler;

/**
 * Configuración automática para listeners de RabbitMQ.
 * Esta clase configura el factory de listeners con manejo de errores apropiado.
 * <AUTHOR>
 */
@AutoConfiguration
@AutoConfigureAfter(RabbitMQDefaultConfig.class)
@ConditionalOnClass({SimpleRabbitListenerContainerFactory.class, ConnectionFactory.class})
public class RabbitListenerConfig {

    /**
     * Factory por defecto para listeners simples.
     * Incluye ErrorHandler para manejo automático de errores de conversión.
     * Usar para listeners que NO extienden AbstractRabbitMQListener.
     *
     * @param connectionFactory Factory de conexiones
     * @param messageConverter Conversor de mensajes
     * @param rabbitErrorHandler Manejador de errores
     * @return SimpleRabbitListenerContainerFactory configurado
     */
    @Bean(name = "rabbitListenerContainerFactory")
    @ConditionalOnMissingBean(name = "rabbitListenerContainerFactory")
    public SimpleRabbitListenerContainerFactory rabbitListenerContainerFactory(
            ConnectionFactory connectionFactory,
            MessageConverter messageConverter,
            ErrorHandler rabbitErrorHandler) {

        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        // factory.setMessageConverter(messageConverter);
        factory.setErrorHandler(rabbitErrorHandler);
        factory.setDefaultRequeueRejected(false);

        return factory;
    }

    /**
     * Factory específico para listeners que extienden AbstractRabbitMQListener.
     * NO incluye ErrorHandler para evitar conflictos con el manejo manual de errores.
     * Usar con containerFactory = "manualListenerContainerFactory".
     *
     * @param connectionFactory Factory de conexiones
     * @param messageConverter Conversor de mensajes
     * @return SimpleRabbitListenerContainerFactory sin ErrorHandler
     */
    @Bean(name = "manualListenerContainerFactory")
    @ConditionalOnMissingBean(name = "manualListenerContainerFactory")
    public SimpleRabbitListenerContainerFactory manualListenerContainerFactory(
            ConnectionFactory connectionFactory,
            MessageConverter messageConverter) {

        SimpleRabbitListenerContainerFactory factory = new SimpleRabbitListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory);
        factory.setMessageConverter(messageConverter);
        // NO ErrorHandler - el manejo se hace en AbstractRabbitMQListener
        factory.setAcknowledgeMode(org.springframework.amqp.core.AcknowledgeMode.MANUAL);
        factory.setDefaultRequeueRejected(false);

        return factory;
    }
}
