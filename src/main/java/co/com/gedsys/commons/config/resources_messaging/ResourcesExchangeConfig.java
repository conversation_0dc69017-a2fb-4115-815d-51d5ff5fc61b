package co.com.gedsys.commons.config.resources_messaging;

import co.com.gedsys.commons.constant.amqp.ExchangeName;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
@ConditionalOnClass({ TopicExchange.class, ConnectionFactory.class })
public class ResourcesExchangeConfig {

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(ResourcesExchangeConfig.class);

    /**
     * Crea el exchange para recursos
     *
     * @return TopicExchange configurado para recursos
     */
    @Bean
    @ConditionalOnMissingBean(name = "recursosTopicExchange")
    public TopicExchange recursosTopicExchange() {
        log.info("Creando exchange para recursos: {}", ExchangeName.TOPIC_RECURSOS);
        return new TopicExchange(ExchangeName.TOPIC_RECURSOS, true, false);
    }
}
