package co.com.gedsys.commons.config.resources_messaging;

import co.com.gedsys.commons.constant.amqp.RoutingKeyName;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;

@AutoConfiguration
@AutoConfigureAfter({ResourcesExchangeConfig.class, ResourcesQueueConfig.class})
@ConditionalOnClass({Binding.class, Queue.class, TopicExchange.class})
public class ResourcesBindingConfig {

    /**
     * Configura el binding para la cola de recursos con el exchange de recursos
     * usando la routing key RESOURCES
     * @param recursosQueue Cola de recursos
     * @param recursosTopicExchange Exchange de recursos
     * @return Binding configurado
     */
    @Bean(name = "resourcesBinding")
    @ConditionalOnMissingBean(name = "resourcesBinding")
    Binding resourceBinding(
            Queue resourcesQueue,
            TopicExchange recursosTopicExchange
    ) {
        return BindingBuilder
                .bind(resourcesQueue)
                .to(recursosTopicExchange)
                .with(RoutingKeyName.CORE_RESOURCE_EVENT_TRIGGERED);
    }
}
