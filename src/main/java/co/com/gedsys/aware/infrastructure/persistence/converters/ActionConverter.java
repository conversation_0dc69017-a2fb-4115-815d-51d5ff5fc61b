package co.com.gedsys.aware.infrastructure.persistence.converters;

import co.com.gedsys.aware.ports.models.Action;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import jakarta.persistence.AttributeConverter;
import jakarta.persistence.Converter;

@Converter(autoApply = false)
public class ActionConverter implements AttributeConverter<Action, String> {

    private static final ObjectMapper mapper = new ObjectMapper()
            .registerModule(new JavaTimeModule())
            .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
            .disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);

    @Override
    public String convertToDatabaseColumn(Action action) {
        if (action == null) {
            return null;
        }
        try {
            return mapper.writeValueAsString(action);
        } catch (JsonProcessingException e) {
            throw new IllegalStateException("Error serializing action to JSON", e);
        }
    }

    @Override
    public Action convertToEntityAttribute(String dbData) {
        if (dbData == null) {
            return null;
        }
        try {
            return mapper.readValue(dbData, Action.class);
        } catch (JsonProcessingException e) {
            throw new IllegalStateException("Error deserializing JSON to Action", e);
        }
    }
}
