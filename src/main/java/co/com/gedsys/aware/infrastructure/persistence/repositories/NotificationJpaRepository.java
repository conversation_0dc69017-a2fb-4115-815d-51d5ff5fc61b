package co.com.gedsys.aware.infrastructure.persistence.repositories;

import co.com.gedsys.aware.infrastructure.persistence.entities.NotificationEntity;
import co.com.gedsys.aware.ports.models.NotificationStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;

import java.time.LocalDateTime;
import java.util.UUID;

@EnableJpaRepositories()
public interface NotificationJpaRepository extends JpaRepository<NotificationEntity, UUID> {

    Page<NotificationEntity> findByOwnerAndStatusAndTimestampBetweenOrderByTimestampDesc(
            String owner,
            NotificationStatus status,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Pageable pageable
    );

    Page<NotificationEntity> findByOwnerAndTimestampBetweenOrderByTimestampDesc(
            String owner,
            LocalDateTime startDate,
            LocalDateTime endDate,
            Pageable pageable
    );

    Page<NotificationEntity> findByOwnerAndStatusOrderByTimestampDesc(
            String owner,
            NotificationStatus status,
            Pageable pageable
    );

    Page<NotificationEntity> findByOwnerOrderByTimestampDesc(
            String owner,
            Pageable pageable
    );
}
