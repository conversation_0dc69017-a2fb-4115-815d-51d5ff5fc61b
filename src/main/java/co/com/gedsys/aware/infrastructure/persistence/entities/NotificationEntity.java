package co.com.gedsys.aware.infrastructure.persistence.entities;

import co.com.gedsys.aware.infrastructure.persistence.converters.ActionConverter;
import co.com.gedsys.aware.infrastructure.persistence.converters.DetailConverter;
import co.com.gedsys.aware.ports.models.Action;
import co.com.gedsys.aware.ports.models.NotificationStatus;
import co.com.gedsys.aware.ports.models.NotificationType;
import jakarta.persistence.*;
import lombok.*;
import org.hibernate.type.SqlTypes;
import org.hibernate.annotations.JdbcTypeCode;

import java.time.LocalDateTime;
import java.util.UUID;

@Getter
@Setter
@Entity
@Table(name = "notifications")
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NotificationEntity {

    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(columnDefinition = "uuid")
    private UUID id;

    @Column(nullable = false)
    private NotificationType type;

    @Column(nullable = false)
    private String title;

    @Column(nullable = false)
    private String owner;

    @Column(nullable = false)
    private LocalDateTime timestamp;

    @Convert(converter = ActionConverter.class)
    @Column(columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    private Action action;

    @Column(nullable = false)
    private NotificationStatus status;

    @Convert(converter = DetailConverter.class)
    @Column(columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    private Object details;


}
