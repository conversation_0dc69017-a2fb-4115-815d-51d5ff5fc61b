package co.com.gedsys.aware.adapters.outbound.services;

import co.com.gedsys.aware.adapters.outbound.repositories.NotificationPostgresRepository;
import co.com.gedsys.aware.ports.models.*;
import co.com.gedsys.aware.ports.outbound.NotificationAlert;

import java.util.UUID;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

@Service
public class NotificationServices implements NotificationAlert {

    @Autowired
    private NotificationPostgresRepository repository;


    @Override
    public Notification saveNotification(Notification notification)  {
        return repository.save(notification);
    }

    @Override
    public Page<Notification> findNotificationsWithFilters(String username, NotificationStatus status, String startDate, String endDate, Pageable pageable) {
        return repository.findNotificationsWithFilters(username, status, startDate, endDate, pageable);
    }

    @Override
    public void markAsRead(UUID notificationId) {
        Notification notification = repository.findById(notificationId);
        notification.setStatus(NotificationStatus.read);
        repository.save(notification);
    }


}
