package co.com.gedsys.aware.adapters.mappers;

import co.com.gedsys.aware.infrastructure.persistence.entities.NotificationEntity;
import co.com.gedsys.aware.ports.models.Notification;

import java.util.List;

public class NotificationMapper {

    public static Notification toDomain(NotificationEntity entity) {
        if (entity == null) {
            return null;
        }

        Notification notification = new Notification();
        notification.setId(entity.getId());
        notification.setType(entity.getType());
        notification.setTitle(entity.getTitle());
        notification.setTimestamp(entity.getTimestamp());
        notification.setAction(entity.getAction());
        notification.setStatus(entity.getStatus());
        notification.setOwner(entity.getOwner());
        notification.setDetails(entity.getDetails());
        return notification;
    }

    public static NotificationEntity toEntity(Notification notification) {
        if (notification == null) {
            return null;
        }

        NotificationEntity entity = new NotificationEntity();
        entity.setId(notification.getId());
        entity.setType(notification.getType());
        entity.setTitle(notification.getTitle());
        entity.setTimestamp(notification.getTimestamp());
        entity.setAction(notification.getAction());
        entity.setStatus(notification.getStatus());
        entity.setOwner(notification.getOwner());
        entity.setDetails(notification.getDetails());
        return entity;
    }

    public static List<Notification> toDomain(List<NotificationEntity> entities) {
        return entities.stream().map(NotificationMapper::toDomain).toList();
    }
}
