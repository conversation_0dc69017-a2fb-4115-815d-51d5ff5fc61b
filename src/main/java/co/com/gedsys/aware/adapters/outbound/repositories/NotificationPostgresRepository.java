package co.com.gedsys.aware.adapters.outbound.repositories;

import co.com.gedsys.aware.adapters.mappers.NotificationMapper;
import co.com.gedsys.aware.infrastructure.persistence.entities.NotificationEntity;
import co.com.gedsys.aware.infrastructure.persistence.repositories.NotificationJpaRepository;
import co.com.gedsys.aware.ports.models.Notification;
import co.com.gedsys.aware.ports.models.NotificationStatus;
import co.com.gedsys.aware.ports.outbound.repositories.NotificationRepository;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
@Repository
public class NotificationPostgresRepository implements NotificationRepository {

    private final NotificationJpaRepository notificationMrRepository;

    public NotificationPostgresRepository(NotificationJpaRepository notificationMrRepository) {
        this.notificationMrRepository = notificationMrRepository;
    }

    @Override
    public Optional<List<Notification>> findAll() {
        return Optional.of(NotificationMapper.toDomain(notificationMrRepository.findAll()));
    }

    @Override
    public Notification save(Notification notification) {
        NotificationEntity saved = notificationMrRepository.save(NotificationMapper.toEntity(notification));
        return NotificationMapper.toDomain(saved);
    }

    @Override
    public Page<Notification> findNotificationsWithFilters(String username, NotificationStatus status, String startDate, String endDate, Pageable pageable) {

        LocalDateTime start = (startDate != null && !startDate.isEmpty()) ?
            LocalDate.parse(startDate).atStartOfDay() : null;
        LocalDateTime end = (endDate != null && !endDate.isEmpty()) ?
            LocalDate.parse(endDate).atTime(23, 59, 59) : null;

        Page<NotificationEntity> result;

        if (status != null && start != null && end != null) {
            result = notificationMrRepository.findByOwnerAndStatusAndTimestampBetweenOrderByTimestampDesc(
                username, status, start, end, pageable);
        } else if (start != null && end != null) {
            result = notificationMrRepository.findByOwnerAndTimestampBetweenOrderByTimestampDesc(
                username, start, end, pageable);
        } else if (status != null) {
            result = notificationMrRepository.findByOwnerAndStatusOrderByTimestampDesc(
                username, status, pageable);
        } else {
            result = notificationMrRepository.findByOwnerOrderByTimestampDesc(
                username, pageable);
        }

        List<Notification> mapped = result.getContent().stream()
                .map(NotificationMapper::toDomain)
                .toList();

        return new PageImpl<>(mapped, pageable, result.getTotalElements());
    }

    @Override
    public Notification findById(UUID id) {
        return notificationMrRepository.findById(id)
                .map(NotificationMapper::toDomain)
                .orElse(null);
    }
}
