package co.com.gedsys.aware.ports.outbound.repositories;

import co.com.gedsys.aware.ports.models.Notification;
import co.com.gedsys.aware.ports.models.NotificationStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.UUID;

public interface NotificationRepository {
    Optional<List<Notification>> findAll();
    Notification save(Notification notification);
    Page<Notification> findNotificationsWithFilters(String username, NotificationStatus status, String startDate, String endDate, Pageable pageable);
    Notification findById(UUID id);

}
