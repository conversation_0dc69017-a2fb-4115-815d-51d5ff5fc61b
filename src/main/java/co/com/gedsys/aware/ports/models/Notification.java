package co.com.gedsys.aware.ports.models;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.UUID;


@Getter
@Setter
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class Notification {
    private UUID id;
    private NotificationType type;
    private String title;
    private LocalDateTime timestamp;
    private Action action;
    private NotificationStatus status;
    private String owner;
    private Object details;
}
