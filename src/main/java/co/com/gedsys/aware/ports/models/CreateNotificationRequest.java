package co.com.gedsys.aware.ports.models;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

@Getter
@Setter
public class CreateNotificationRequest {

    @NotNull(message = "El tipo de notificación es obligatorio")
    private NotificationType type;

    @NotBlank(message = "El título es obligatorio")
    @Size(max = 255, message = "El título no puede exceder 255 caracteres")
    private String title;

    @NotEmpty(message = "Debe especificar al menos un stakeholder")
    @Size(max = 100, message = "No se pueden especificar más de 100 stakeholders")
    private List<@NotBlank(message = "Los stakeholders no pueden estar vacíos") String> stakeholders;

    @Valid
    @NotNull(message = "La acción es obligatoria")
    private Action action;

    private Object details;
}
