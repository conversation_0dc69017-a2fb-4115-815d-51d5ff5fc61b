package co.com.gedsys.aware.ports.models;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

public record Action(
        @NotNull(message = "El tipo de acción es obligatorio")
        ActionType type,

        @NotBlank(message = "La URL es obligatoria")
        @Size(max = 500, message = "La URL no puede exceder 500 caracteres")
        String url,

        @NotBlank(message = "El texto de la acción es obligatorio")
        @Size(max = 100, message = "El texto de la acción no puede exceder 100 caracteres")
        String text
) {
    public Action(ActionType type, String path, ActionText text) {
        this(type, path, text.name());
    }
}
