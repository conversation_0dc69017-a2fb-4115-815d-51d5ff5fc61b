package co.com.gedsys.aware.ports.outbound;

import co.com.gedsys.aware.ports.models.Notification;
import co.com.gedsys.aware.ports.models.NotificationStatus;

import com.fasterxml.jackson.core.JsonProcessingException;

import java.util.UUID;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

public interface NotificationAlert  {
    Notification saveNotification(Notification notification) throws JsonProcessingException;
    Page<Notification> findNotificationsWithFilters(String username, NotificationStatus status, String startDate, String endDate, Pageable pageable);
    void markAsRead(UUID notificationId);
}
