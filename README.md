# gedsys2-commons

Configuración automática para RabbitMQ con manejo de errores y Dead Letter Queue.

## Configuración automática

### RabbitMQ
- **MessageConverter**: <PERSON> con trust all habilitado
- **Error<PERSON>andler**: Envío automático a DLQ en errores de conversión
- **Dead Letter Queue**: Configuración automática para mensajes fallidos

### Uso en aplicaciones cliente

```yaml
spring:
  rabbitmq:
    listener:
      simple:
        acknowledge-mode: manual
        default-requeue-rejected: false
```

#### Opción A: Listener simple (recomendado para casos básicos)
```java
@RabbitListener(queues = "mi.cola")
public void handleMessage(Object payload) {
    // ErrorHandler maneja errores de conversión automáticamente
    // Solo errores de negocio pueden ocurrir aquí
}
```

#### Opción B: Listener con AbstractRabbitMQListener (para control avanzado)
```java
@Component
public class DocumentaryLoadListener extends AbstractRabbitMQListener<DocumentaryLoadCompleted> {

    public DocumentaryLoadListener(RabbitTemplate rabbitTemplate) {
        super(rabbitTemplate);
    }

    @RabbitListener(
        queues = "procedures.documentry.load.completed",
        containerFactory = "manualListenerContainerFactory"  // ← Factory sin ErrorHandler
    )
    public void listen(DocumentaryLoadCompleted payload, Message message,
                      Channel channel, @Header(AmqpHeaders.DELIVERY_TAG) long deliveryTag) {
        processMessage(payload, message, channel, deliveryTag);
    }

    @Override
    protected void handleMessageProcessing(DocumentaryLoadCompleted event) throws IOException {
        // Lógica de negocio con retry automático (3 intentos)
        log.info("Processing documentary load: {}", event.processId());
    }
}
```

### ⚠️ Importante: No mezclar enfoques
- **Opción A**: Usa `rabbitListenerContainerFactory` (por defecto)
- **Opción B**: Usa `manualListenerContainerFactory` obligatoriamente
- **Nunca** usar AbstractRabbitMQListener sin especificar `containerFactory`

## Constantes

### Exchanges
- `TOPIC_DOCUMENTOS`, `TOPIC_RECURSOS`, `TOPIC_PROCESOS`

### Colas
- `TAREAS_ACTUALIZADAS`, `PROCEDURES`, `DOCUMENTARY_LOAD_COMPLETED`
- `CORE_RESOURCES`

### Dead Letter
- `DLX`: gedsys.dlx
- `DLQ`: gedsys.dlq
- `DLK`: gedsys.dlk

### Routing Keys
- `TAREA_ACTUALIZADA`, `PROCEDURE_UPDATED`, `PROCEDURE_RESUMED`
- `CORE_RESOURCE_EVENT_TRIGGERED`

## Eventos

### Procesos
- `DocumentaryLoadCompleted`: Carga documental completada
- `TaskEvent`: Eventos de tareas BPM

### Recursos
- `ResourceEvent`: Eventos de trazabilidad de recursos

### Identidad
- `UserLoggedEvent`: Eventos de autenticación

## Manejo de errores

### ErrorHandler (Opción A)
- **Errores de conversión** → DLQ inmediato
- **Errores de negocio** → Excepción al listener

### AbstractRabbitMQListener (Opción B)
- **Errores de conversión** → DLQ inmediato (por ErrorHandler)
- **Errores de negocio** → Retry 3 veces → DLQ
- **Control manual** de acknowledgment

### Tipos de errores manejados automáticamente
- `MessageConversionException` → DLQ
- `Cannot convert from LinkedHashMap` → DLQ
- `is not in the trusted packages` → DLQ