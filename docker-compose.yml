services:
  postgres:
    image: postgres:17-alpine
    container_name: gedsys2-aware-postgres
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-aware}
      POSTGRES_USER: ${POSTGRES_USER:-postgres}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-postgres}

  lavinmq:
    image: cloudamqp/lavinmq:latest
    container_name: gedsys2-aware-lavinmq
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      LAVINMQ_DEFAULT_USER: ${RABBITMQ_USER:-guest}
      LAVINMQ_DEFAULT_PASS: ${RABBITMQ_PASSWORD:-guest}
    healthcheck:
      test: ["CMD", "lavinmqctl", "status"]
      interval: 30s
      timeout: 10s
      retries: 5