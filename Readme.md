# GEDSYS2 Aware

## Introduction

Notifications and alerts Microservice

## Requirements

- Java 21
- Maven
- AWS SES account for production use

##  Settings

```yaml
spring:
  datasource:
    url: jdbc:postgresql://<<POSTGRES_HOST>>:<<POSTGRES_PORT>>/<<POSTGRES_DB>>
    username: <<POSTGRES_USER>>
    password: <<POSTGRES_PASSWORD>>
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
  rabbitmq:
    host: <<RABBITMQ_HOST>>
    port: <<RABBITMQ_PORT>>
    username: <<RABBITMQ_USER>>
    password: <<RABBITMQ_PASSWORD>>
```

**Note:** For production use, disable docker compose in `application.yaml`

### Message Example
```
{
  "taskId": "12345",
  "taskFormKey": "formularioRedaccion",
  "assigned": "dplata",
  "timestamp": "2025-01-27T14:30:00Z"
}
```

## Deployment

Create a new properties file for deployment configurations, including environment variables, and name it `application-prod.yml` or as appropriate.




