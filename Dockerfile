FROM maven:3-amazoncorretto-21 AS builder
WORKDIR /build

ARG MAVEN_USERNAME
ARG MAVEN_PASSWORD

RUN mkdir -p /root/.m2 \
    && echo "<settings><servers><server><id>github</id><username>${MAVEN_USERNAME}</username><password>${MAVEN_PASSWORD}</password></server></servers></settings>" > /root/.m2/settings.xml

COPY ./pom.xml .
RUN mvn dependency:go-offline

COPY ./src ./src
RUN mvn clean package -DskipTests

FROM amazoncorretto:21-alpine

ENV JAVA_OPTS="-XX:+UseContainerSupport \
               -XX:MaxRAMPercentage=75 \
               -XX:InitialRAMPercentage=50 \
               -XX:+OptimizeStringConcat \
               -XX:+UseStringDeduplication \
               -Djava.security.egd=file:/dev/./urandom"
ENV SPRING_ARGS=""

WORKDIR /app
RUN addgroup -S spring && adduser -S spring -G spring
RUN chown -R spring:spring /app
USER spring
COPY --from=builder /build/target/*.jar /app/app.jar

EXPOSE 8080

ENTRYPOINT ["sh", "-c"]
CMD ["java $JAVA_OPTS -jar $SPRING_ARGS app.jar"]